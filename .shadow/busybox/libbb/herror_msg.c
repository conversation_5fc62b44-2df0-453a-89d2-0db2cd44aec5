/* vi: set sw=4 ts=4: */
/*
 * Utility routines.
 *
 * Copyright (C) 1999-2004 by <PERSON> <<EMAIL>>
 *
 * Licensed under GPLv2 or later, see file LICENSE in this source tree.
 */
#include "libbb.h"

void FAST_FUNC bb_herror_msg(const char *s, ...)
{
	va_list p;

	va_start(p, s);
	bb_verror_msg(s, p, hstrerror(h_errno));
	va_end(p);
}

void FAST_FUNC bb_herror_msg_and_die(const char *s, ...)
{
	va_list p;

	va_start(p, s);
	bb_verror_msg(s, p, hstrerror(h_errno));
	va_end(p);
	xfunc_die();
}

void FAST_FUNC bb_simple_herror_msg(const char *s)
{
	bb_herror_msg("%s", s);
}

void FAST_FUNC bb_simple_herror_msg_and_die(const char *s)
{
	bb_herror_msg_and_die("%s", s);
}
