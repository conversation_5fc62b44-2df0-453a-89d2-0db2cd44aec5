/* vi: set sw=4 ts=4: */
/*
 * Utility routines.
 *
 * Copyright (C) 2008 by <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under GPLv2, see file LICENSE in this source tree.
 */
#include "libbb.h"

/* Keeping it separate allows to NOT pull in stdio for VERY small applets.
 * Try building busybox with only "true" enabled... */

void (*die_func)(void);

void FAST_FUNC xfunc_die(void)
{
	if (die_func)
		die_func();
	exit(xfunc_error_retval);
}
