/* vi: set sw=4 ts=4: */
/*
 * Mini xgethostbyname implementation.
 *
 * Copyright (C) 2001 <PERSON> <kra<PERSON>@alumni.carnegiemellon.edu>.
 *
 * Licensed under GPLv2 or later, see file LICENSE in this source tree.
 */
#include "libbb.h"

struct hostent* FAST_FUNC xgethostbyname(const char *name)
{
	struct hostent *retval = gethostbyname(name);
	if (!retval)
		bb_simple_herror_msg_and_die(name);
	return retval;
}
